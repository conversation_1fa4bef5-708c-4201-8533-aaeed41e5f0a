### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap
  commentId: T:DrawnUi.Draw.SkiaImage.RescaledBitmap
  id: SkiaImage.RescaledBitmap
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap
  - DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose
  - DrawnUi.Draw.SkiaImage.RescaledBitmap.Image
  - DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality
  - DrawnUi.Draw.SkiaImage.RescaledBitmap.Source
  - DrawnUi.Draw.SkiaImage.RescaledBitmap._image
  langs:
  - csharp
  - vb
  name: SkiaImage.RescaledBitmap
  nameWithType: SkiaImage.RescaledBitmap
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RescaledBitmap
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1349
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class SkiaImage.RescaledBitmap : IDisposable'
    content.vb: Public Class SkiaImage.RescaledBitmap Implements IDisposable
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap
  commentId: P:DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap
  id: Bitmap
  parent: DrawnUi.Draw.SkiaImage.RescaledBitmap
  langs:
  - csharp
  - vb
  name: Bitmap
  nameWithType: SkiaImage.RescaledBitmap.Bitmap
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bitmap
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1351
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKBitmap Bitmap { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKBitmap
    content.vb: Public Property Bitmap As SKBitmap
  overload: DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap*
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap._image
  commentId: F:DrawnUi.Draw.SkiaImage.RescaledBitmap._image
  id: _image
  parent: DrawnUi.Draw.SkiaImage.RescaledBitmap
  langs:
  - csharp
  - vb
  name: _image
  nameWithType: SkiaImage.RescaledBitmap._image
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap._image
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _image
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1353
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKImage _image
    return:
      type: SkiaSharp.SKImage
    content.vb: Public _image As SKImage
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Image
  commentId: P:DrawnUi.Draw.SkiaImage.RescaledBitmap.Image
  id: Image
  parent: DrawnUi.Draw.SkiaImage.RescaledBitmap
  langs:
  - csharp
  - vb
  name: Image
  nameWithType: SkiaImage.RescaledBitmap.Image
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Image
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Image
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1355
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKImage Image { get; }
    parameters: []
    return:
      type: SkiaSharp.SKImage
    content.vb: Public ReadOnly Property Image As SKImage
  overload: DrawnUi.Draw.SkiaImage.RescaledBitmap.Image*
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality
  commentId: P:DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality
  id: Quality
  parent: DrawnUi.Draw.SkiaImage.RescaledBitmap
  langs:
  - csharp
  - vb
  name: Quality
  nameWithType: SkiaImage.RescaledBitmap.Quality
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Quality
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1368
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKFilterQuality Quality { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKFilterQuality
    content.vb: Public Property Quality As SKFilterQuality
  overload: DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality*
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Source
  commentId: P:DrawnUi.Draw.SkiaImage.RescaledBitmap.Source
  id: Source
  parent: DrawnUi.Draw.SkiaImage.RescaledBitmap
  langs:
  - csharp
  - vb
  name: Source
  nameWithType: SkiaImage.RescaledBitmap.Source
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Source
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Source
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1369
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Guid Source { get; set; }
    parameters: []
    return:
      type: System.Guid
    content.vb: Public Property Source As Guid
  overload: DrawnUi.Draw.SkiaImage.RescaledBitmap.Source*
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose
  commentId: M:DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose
  id: Dispose
  parent: DrawnUi.Draw.SkiaImage.RescaledBitmap
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: SkiaImage.RescaledBitmap.Dispose()
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Images/SkiaImage.cs
    startLine: 1371
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose*
  implements:
  - System.IDisposable.Dispose
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap*
  commentId: Overload:DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap
  href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html#DrawnUi_Draw_SkiaImage_RescaledBitmap_Bitmap
  name: Bitmap
  nameWithType: SkiaImage.RescaledBitmap.Bitmap
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Bitmap
- uid: SkiaSharp.SKBitmap
  commentId: T:SkiaSharp.SKBitmap
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  name: SKBitmap
  nameWithType: SKBitmap
  fullName: SkiaSharp.SKBitmap
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: SkiaSharp.SKImage
  commentId: T:SkiaSharp.SKImage
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skimage
  name: SKImage
  nameWithType: SKImage
  fullName: SkiaSharp.SKImage
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Image*
  commentId: Overload:DrawnUi.Draw.SkiaImage.RescaledBitmap.Image
  href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html#DrawnUi_Draw_SkiaImage_RescaledBitmap_Image
  name: Image
  nameWithType: SkiaImage.RescaledBitmap.Image
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Image
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality*
  commentId: Overload:DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality
  href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html#DrawnUi_Draw_SkiaImage_RescaledBitmap_Quality
  name: Quality
  nameWithType: SkiaImage.RescaledBitmap.Quality
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Quality
- uid: SkiaSharp.SKFilterQuality
  commentId: T:SkiaSharp.SKFilterQuality
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skfilterquality
  name: SKFilterQuality
  nameWithType: SKFilterQuality
  fullName: SkiaSharp.SKFilterQuality
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Source*
  commentId: Overload:DrawnUi.Draw.SkiaImage.RescaledBitmap.Source
  href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html#DrawnUi_Draw_SkiaImage_RescaledBitmap_Source
  name: Source
  nameWithType: SkiaImage.RescaledBitmap.Source
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Source
- uid: System.Guid
  commentId: T:System.Guid
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.guid
  name: Guid
  nameWithType: Guid
  fullName: System.Guid
- uid: DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose*
  commentId: Overload:DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose
  href: DrawnUi.Draw.SkiaImage.RescaledBitmap.html#DrawnUi_Draw_SkiaImage_RescaledBitmap_Dispose
  name: Dispose
  nameWithType: SkiaImage.RescaledBitmap.Dispose
  fullName: DrawnUi.Draw.SkiaImage.RescaledBitmap.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
