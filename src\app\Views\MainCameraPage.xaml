<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="ShadersCamera.Views.MainCameraPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="clr-namespace:ShadersCamera.ViewModels"
    NavigationPage.HasNavigationBar="False"
    xmlns:models="clr-namespace:ShadersCamera.Models"
    xmlns:controls="clr-namespace:ShadersCamera.Views.Controls"
    x:Name="ThisPage"

    x:DataType="viewModels:CameraViewModel">

    <draw:Canvas
        BackgroundColor="Black"
        ViewDisposing="CanvasWillDispose"
        WillFirstTimeDraw="WillFirstTimeDraw"
        Gestures="Enabled"
        HorizontalOptions="Fill" VerticalOptions="Fill"
        RenderingMode="Accelerated"
        Tag="MainPage">

        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

            <draw:SkiaLayer
                Tapped="TappedBackground"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <!--  CAMERA PREVIEW ETC  -->
                <controls:CameraWithEffects
                    ShaderSource="{Binding SelectedShader}"
                    ConstantUpdate="False"
                    x:Name="CameraControl"
                    BackgroundColor="Black"
                    CapturePhotoQuality="Medium"
                    Facing="Default"
                    HorizontalOptions="Fill"
                    Tag="Camera"
                    VerticalOptions="Fill"
                    ZIndex="-1"
                    ZoomLimitMax="10"
                    ZoomLimitMin="1" />

                <!--  CACHED LAYER CONTROLS  -->
                <draw:SkiaLayer
                    UseCache="Operations"
                    VerticalOptions="Fill">

                    <!--BTNS CONTROLS-->
                    <draw:SkiaShape HorizontalOptions="Center"
                                    VerticalOptions="End"
                                    Margin="0,0,0,24"
                                    Padding="8,0"
                                    HeightRequest="60"
                                    StrokeColor="Black"
                                    StrokeWidth="-1"
                                    BackgroundColor="#66000000"
                                    CornerRadius="32">


                        <draw:SkiaRow
                            UseCache="GPU"
                            Padding="1"
                            Spacing="10"
                            HorizontalOptions="Center" 
                            VerticalOptions="Center">

                            <!--last photo tappable preview-->
                            <draw:SkiaShape
                                draw:AddGestures.CommandTapped="{Binding CommandPreviewTapped}"
                                StrokeColor="#66CECECE"
                                StrokeWidth="1"
                                Type="Circle" HeightRequest="46" LockRatio="1"
                                BackgroundColor="#66000000"
                                IsClippedToBounds="True"
                                UseCache="Image"
                                VerticalOptions="Start"
                                WidthRequest="46">

                                <draw:SkiaImage
                                    RescalingQuality="None"
                                    x:Name="ImagePreview"
                                    Aspect="AspectCover"
                                    HorizontalOptions="Fill"
                                    ImageBitmap="{Binding DisplayPreview}"
                                    Tag="Preview"
                                    VerticalOptions="Fill" />

                            </draw:SkiaShape>

                            <!--SETTINGS-->
                            <draw:SkiaShape
                                StrokeColor="#66CECECE"
                                StrokeWidth="1"
                                UseCache="Image"
                                Tapped="TappedSettings"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">
                                <draw:SkiaSvg
                                    SvgString="{x:StaticResource SvgSettings}"
                                    TintColor="#CECECE"
                                    WidthRequest="18"
                                    LockRatio="1"
                                    HorizontalOptions="Center" VerticalOptions="Center" />
                            </draw:SkiaShape>

                            <!--POWER/PAUSE-->
                            <draw:SkiaShape
                                UseCache="Image"
                                IsVisible="False"
                                Tapped="TappedTurnCamera"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="P"
                                                TextColor="White" />

                            </draw:SkiaShape>

                            <!--EFFECTS-->
                            <draw:SkiaShape
                                UseCache="Image"
                                IsVisible="False"
                                Tapped="TappedCycleEffects"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="E"
                                                TextColor="White" />

                            </draw:SkiaShape>

                            <!--FLASH CAPTURE-->
                            <draw:SkiaShape
                                StrokeColor="#66CECECE"
                                StrokeWidth="1"
                                UseCache="Image"
                                Tapped="OnFlashClicked"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaSvg
                                    x:Name="SvgFlashCapture"
                                    SvgString="{x:StaticResource SvgFlashAuto}"
                                    TintColor="#CECECE"
                                    WidthRequest="19"
                                    LockRatio="1"
                                    HorizontalOptions="Center" VerticalOptions="Center" />
                                <!--<draw:SkiaLabel
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center" Text="F" TextColor="White" />-->

                            </draw:SkiaShape>

                            <!--FLASH LIGHT-->
                            <draw:SkiaShape
                                StrokeColor="#CECECE"
                                StrokeWidth="1"
                                IsVisible="False"
                                UseCache="Image"
                                Tapped="TappedFlash"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaSvg
                                    x:Name="SvgFlashLight"
                                    SvgString="{x:StaticResource SvgLightOff}"
                                    TintColor="#CECECE"
                                    WidthRequest="18"
                                    LockRatio="1"
                                    HorizontalOptions="Center" VerticalOptions="Center" />

                                <!--<draw:SkiaLabel
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center" Text="L" TextColor="White" />-->

                            </draw:SkiaShape>

                            <!--SOURCE/SWITCH-->
                            <draw:SkiaShape
                                StrokeColor="#66CECECE"
                                StrokeWidth="1"
                                UseCache="Image"
                                Tapped="TappedSwitchCamera"
                                Type="Circle" HeightRequest="46" LockRatio="1" BackgroundColor="Black">

                                <draw:SkiaSvg
                                    SvgString="{x:StaticResource SvgSource}"
                                    TintColor="#CECECE"
                                    WidthRequest="18"
                                    LockRatio="1"
                                    HorizontalOptions="Center" VerticalOptions="Center" />


                                <!--<draw:SkiaLabel VerticalOptions="Center" HorizontalOptions="Center" Text="S"
                                                TextColor="White" />-->

                            </draw:SkiaShape>

                            <!--CAPTURE-->
                            <draw:SkiaShape
                                UseCache="Image"
                                draw:AddGestures.CommandTapped="{Binding CommandCaptureStillPhoto}"
                                Type="Circle" HeightRequest="46" LockRatio="1" StrokeWidth="2" StrokeColor="#66CECECE"
                                BackgroundColor="Black"
                                Padding="3">

                                <draw:SkiaShape
                                    x:Name="ButtonCapture"
                                    BackgroundColor="#CECECE"
                                    Type="Circle" HorizontalOptions="Fill" VerticalOptions="Fill" />

                            </draw:SkiaShape>

                        </draw:SkiaRow>

                    </draw:SkiaShape>

                    <!--tap to resume after cam paused-->
                    <draw:SkiaHotspot
                        HorizontalOptions="Center"
                        IsVisible="{Binding ShowResume}"
                        LockRatio="1"
                        Tapped="TappedResume"
                        VerticalOptions="Center"
                        WidthRequest="290"
                        ZIndex="110" />

                </draw:SkiaLayer>

                <!--  catch pinch to zoom  -->
                <draw:SkiaHotspotZoom
                    ZoomMax="3"
                    ZoomMin="1"
                    Zoomed="OnZoomed" />

            </draw:SkiaLayer>

            <!--  SHADER SELECTOR DRAWER  -->
            <draw:SkiaDrawer
                Margin="0,0,0,100"
                x:Name="ShaderDrawer"
                HeaderSize="40"
                Direction="FromLeft"
                VerticalOptions="End"
                HorizontalOptions="Fill"
                HeightRequest="100"
                IsOpen="False"
                IgnoreWrongDirection="True"
                ZIndex="50">

                <!--  DRAWER CONTENT  -->
                <draw:SkiaShape
                    Type="Rectangle"
                    CornerRadius="0,12,12,0"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <!--  SHADER ITEMS SCROLL  -->
                        <draw:SkiaScroll
                            RespondsToGestures="{Binding Source={x:Reference ShaderDrawer}, Path=IsOpen}"
                            BackgroundColor="WhiteSmoke"
                            Margin="0,0,20,0"
                            Orientation="Horizontal"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill"
                            Padding="8">

                            <controls:SkiaLayoutWithSelector
                                x:Name="Stack"
                                Type="Row"
                                VerticalOptions="Center"
                                Spacing="8"
                                RecyclingTemplate="Enabled"
                                UseCache="Operations"
                                ItemsSource="{Binding ShaderItems}">

                                <draw:SkiaLayout.ItemTemplate>
                                    <DataTemplate x:DataType="models:ShaderItem">

                                        <draw:SkiaShape
                                            Type="Rectangle"
                                            WidthRequest="80"
                                            HeightRequest="80"
                                            CornerRadius="8"
                                            BackgroundColor="White"
                                            UseCache="Image"
                                            draw:AddGestures.CommandLongPressing="{Binding Source={x:Reference CameraControl}, Path=CommandEditShader}"
                                            draw:AddGestures.CommandTapped="{Binding Source={x:Reference Stack}, Path=BindingContext.CommandSelectShader}">

                                            <!--  SHADER CELL CONTENT  -->
                                            <draw:SkiaLayout
                                                HorizontalOptions="Fill"
                                                VerticalOptions="Fill">

                                                <!--  IMAGE WITH SHADER EFFECT  -->
                                                <draw:SkiaImage
                                                    RescalingQuality="None"
                                                    Source="{Binding ImageSource}"
                                                    ImageBitmap="{Binding  Source ={x:Reference ThisPage}, Path=SmallPreview}"
                                                    Aspect="AspectCover"
                                                    HorizontalOptions="Fill"
                                                    VerticalOptions="Fill"
                                                    UseCache="Image">

                                                    <draw:SkiaImage.VisualEffects>
                                                        <draw:SkiaShaderEffect ShaderSource="{Binding Filename}" />
                                                    </draw:SkiaImage.VisualEffects>

                                                </draw:SkiaImage>

                                                <!--  SHADOW BACKGROUND FOR TEXT  -->
                                                <draw:SkiaShape
                                                    Type="Rectangle"
                                                    BackgroundColor="#80000000"
                                                    HorizontalOptions="Fill"
                                                    VerticalOptions="End"
                                                    HeightRequest="20" />

                                                <!--  TITLE LABEL  -->
                                                <draw:SkiaLabel
                                                    Text="{Binding Title}"
                                                    FontSize="12"
                                                    TextColor="White"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="End"
                                                    Margin="4"
                                                    HorizontalTextAlignment="Center"
                                                    UseCache="Operations" />

                                            </draw:SkiaLayout>

                                            <!--will not use shadows now-->
                                            <!--<draw:SkiaShape.Shadows>
                                            <draw:SkiaShadow
                                                Blur="4"
                                                Opacity="0.2"
                                                X="0"
                                                Y="2"
                                                Color="Black" />
                                        </draw:SkiaShape.Shadows>-->

                                        </draw:SkiaShape>
                                    </DataTemplate>
                                </draw:SkiaLayout.ItemTemplate>

                            </controls:SkiaLayoutWithSelector>

                        </draw:SkiaScroll>

                        <!--  DRAWER HEADER  -->
                        <draw:SkiaShape
                            Tapped="TappedDrawerHeader"
                            UseCache="Image"
                            HorizontalOptions="End"
                            Type="Rectangle"
                            BackgroundColor="WhiteSmoke"
                            CornerRadius="0,16,0,0"
                            VerticalOptions="Fill"
                            WidthRequest="41">

                            <draw:SkiaLayout
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaShape
                                    Type="Rectangle"
                                    WidthRequest="4"
                                    HeightRequest="40"
                                    BackgroundColor="#CCCCCC"
                                    CornerRadius="2"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center" />

                            </draw:SkiaLayout>

                        </draw:SkiaShape>

                    </draw:SkiaLayout>

                </draw:SkiaShape>

            </draw:SkiaDrawer>

            <draw:SkiaLabelFps
                IsVisible="{Binding IsDebug}"
                Margin="0,0,4,24"
                BackgroundColor="Black"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</ContentPage>