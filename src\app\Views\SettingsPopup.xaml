<?xml version="1.0" encoding="utf-8"?>

<fastPopups:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                  xmlns:fastPopups="clr-namespace:FastPopups;assembly=FastPopups"
                  xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
                  xmlns:strings="clr-namespace:ShadersCamera.Resources.Strings"
                  HorizontalOptions="Center"
                  VerticalOptions="Center"
                  WidthRequest="320"
                  CloseWhenBackgroundIsClicked="True"
                  x:Class="ShadersCamera.Views.SettingsPopup">

    <Grid MaximumHeightRequest="500"
          HorizontalOptions="Fill" VerticalOptions="Start">

        <draw:Canvas Gestures="Lock" HorizontalOptions="Fill" VerticalOptions="Start">

            <draw:SkiaLayout HorizontalOptions="Fill">

                <draw:SkiaShape
                    HorizontalOptions="Fill"
                    Padding="24"
                    BackgroundColor="Black" CornerRadius="10" StrokeWidth="1" StrokeColor="Gray">

                    <draw:SkiaLayout Type="Column" HorizontalOptions="Fill" Spacing="32" Margin="0,0,0,8">

                        <draw:SkiaRichLabel
                            Opacity="0.75"
                            FontFamily="FontTextBold"
                            UseCache="Operations"
                            HorizontalOptions="Center"
                            Text="{x:Static strings:ResStrings.Settings}" />

                        <!--FULLSCREEN-->
                        <draw:SkiaLayout HorizontalOptions="Fill" UseCache="Operations">

                            <draw:SkiaRichLabel
                                UseCache="Operations"
                                VerticalOptions="Center"
                                Text="Fill Screen" />

                            <draw:SkiaSwitch
                                ColorFrameOn="{x:StaticResource ColorPrimary}"
                                Toggled="FullScreenSwitch_OnToggled"
                                x:Name="FullScreenSwitch"
                                HeightRequest="28"
                                WidthRequest="45"
                                HorizontalOptions="End" />

                        </draw:SkiaLayout>

                        <!--MIRROR-->
                        <draw:SkiaLayout HorizontalOptions="Fill" UseCache="Operations">

                            <draw:SkiaRichLabel
                                UseCache="Operations"
                                VerticalOptions="Center"
                                Text="Mirror Preview" />

                            <draw:SkiaSwitch
                                ColorFrameOn="{x:StaticResource ColorPrimary}"
                                Toggled="MirrorSwitch_OnToggled"
                                x:Name="MirrorSwitch"
                                HeightRequest="28"
                                WidthRequest="45"
                                HorizontalOptions="End" />

                        </draw:SkiaLayout>

                        <!--FORMAT-->
                        <draw:SkiaLayout HorizontalOptions="Fill" UseCache="Operations" Tapped="TappedSelectFormat">

                            <draw:SkiaRichLabel
                                UseCache="Operations"
                                Text="Capture Format" />

                            <draw:SkiaLabel
                                Opacity="0.75"
                                x:Name="FormatLabel"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Text="Format" />

                        </draw:SkiaLayout>

                        <!--HELP-->
                        <draw:SkiaLayout HorizontalOptions="Fill" UseCache="Operations" Tapped="TappedSelectHelp">

                            <draw:SkiaRichLabel
                                UseCache="Operations"
                                Text="Help" />

                            <draw:SkiaRichLabel
                                Opacity="0.75"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Text="❔" />

                        </draw:SkiaLayout>

                    </draw:SkiaLayout>

                </draw:SkiaShape>

            </draw:SkiaLayout>

        </draw:Canvas>

    </Grid>

</fastPopups:Popup>