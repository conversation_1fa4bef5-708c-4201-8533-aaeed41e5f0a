﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Addons", "Addons", "{5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DrawnUi.Maui.Camera", "Maui\Addons\DrawnUi.Maui.Camera\DrawnUi.Maui.Camera.csproj", "{DD2D491D-7046-41D2-A00E-FE65CBADE85E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DrawnUi.Maui.Game", "<PERSON>ui\Addons\DrawnUi.Maui.Game\DrawnUi.Maui.Game.csproj", "{793E382E-815C-42A2-B045-44FF930BED07}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DrawnUi.MauiGraphics", "Maui\Addons\DrawnUi.MauiGraphics\DrawnUi.MauiGraphics.csproj", "{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{8ED244C1-6F60-4954-BC7F-98D827EA31CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UnitTests", "Tests\UnitTests\UnitTests.csproj", "{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{089100B1-113F-4E66-888A-E83F3999EAFD}"
	ProjectSection(SolutionItems) = preProject
		Maui\Directory.Build.props = Maui\Directory.Build.props
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		..\nugets\githubupload.bat = ..\nugets\githubupload.bat
		..\nugets\makenugets.bat = ..\nugets\makenugets.bat
		..\nugets\movenugets.bat = ..\nugets\movenugets.bat
		..\nugets\nugetupload.bat = ..\nugets\nugetupload.bat
		..\README.md = ..\README.md
	EndProjectSection
EndProject
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "DrawnUi.Shared", "Shared\DrawnUi.Shared.shproj", "{*************-48DD-BDB3-98EDECBB1107}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrawnUi.Maui.MapsUi", "Maui\Addons\DrawnUi.Maui.MapsUi\DrawnUi.Maui.MapsUi.csproj", "{FADF6785-E873-6CD3-764C-E3A98456EAF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sandbox", "Maui\Samples\Sandbox\Sandbox.csproj", "{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Samples", "Samples", "{5D20AA90-6969-D8BD-9DCD-8634F4692FDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrawnUi.Maui", "Maui\DrawnUi\DrawnUi.Maui.csproj", "{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppoMobi.Maui.DrawnUi", "Maui\MetaPackage\AppoMobi.Maui.DrawnUi\AppoMobi.Maui.DrawnUi.csproj", "{B8F7A5C3-1234-5678-9ABC-DEF012345678}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SomeBenchmarks", "Tests\Benchmarks\SomeBenchmarks.csproj", "{EEC6E610-C1E3-9D71-F4EE-66581B2394A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tutorials", "Maui\Samples\Tutorials\Tutorials.csproj", "{F6E58B0C-85AB-D5A3-0840-88682D38F9CF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Release|Any CPU.Build.0 = Release|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Release|Any CPU.Build.0 = Release|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8F7A5C3-1234-5678-9ABC-DEF012345678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8F7A5C3-1234-5678-9ABC-DEF012345678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8F7A5C3-1234-5678-9ABC-DEF012345678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8F7A5C3-1234-5678-9ABC-DEF012345678}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEC6E610-C1E3-9D71-F4EE-66581B2394A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEC6E610-C1E3-9D71-F4EE-66581B2394A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEC6E610-C1E3-9D71-F4EE-66581B2394A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEC6E610-C1E3-9D71-F4EE-66581B2394A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6E58B0C-85AB-D5A3-0840-88682D38F9CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6E58B0C-85AB-D5A3-0840-88682D38F9CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6E58B0C-85AB-D5A3-0840-88682D38F9CF}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{F6E58B0C-85AB-D5A3-0840-88682D38F9CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6E58B0C-85AB-D5A3-0840-88682D38F9CF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{793E382E-815C-42A2-B045-44FF930BED07} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B} = {8ED244C1-6F60-4954-BC7F-98D827EA31CC}
		{FADF6785-E873-6CD3-764C-E3A98456EAF8} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34} = {5D20AA90-6969-D8BD-9DCD-8634F4692FDA}
		{B8F7A5C3-1234-5678-9ABC-DEF012345678} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{EEC6E610-C1E3-9D71-F4EE-66581B2394A3} = {8ED244C1-6F60-4954-BC7F-98D827EA31CC}
		{F6E58B0C-85AB-D5A3-0840-88682D38F9CF} = {5D20AA90-6969-D8BD-9DCD-8634F4692FDA}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {329E3D0C-A3F7-4A3E-B61C-6B2D1BD7F708}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		Shared\Shared.projitems*{*************-48dd-bdb3-98edecbb1107}*SharedItemsImports = 13
		Shared\Shared.projitems*{93e119b1-4378-87df-2dd2-a818d1e6c2a2}*SharedItemsImports = 5
	EndGlobalSection
EndGlobal
